/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 12:07:28
 * @LastEditors: Ho<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-20 08:59:59
 * @Description:
 */
import router from './router'
import type { RouteRecordRaw } from 'vue-router'
import { isRelogin } from '@/config/axios/service'
import { getAccessToken } from '@/utils/auth'
import { useTitle } from '@/hooks/web/useTitle'
import { useNProgress } from '@/hooks/web/useNProgress'
import { usePageLoading } from '@/hooks/web/usePageLoading'
import { useDictStoreWithOut } from '@/store/modules/dict'
import { useUserStoreWithOut } from '@/store/modules/user'
import { usePermissionStoreWithOut } from '@/store/modules/permission'

import { GetQueryString } from '@/utils/routerHelper'
import { OauthUrlQuery } from '@/api/login/types'
import { handleAuthorized } from '@/config/axios/service'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
import * as authUtil from '@/utils/auth'
import { isEmpty } from 'lodash-es'

//代客操作
import { useAgentOperationStore } from '@/store/modules/agentOperation'

import { useLocaleStore } from '@/store/modules/locale'
import { useLocale } from '@/hooks/web/useLocale'

const localeStore = useLocaleStore()

const { wsCache } = useCache()

const { start, done } = useNProgress()

const { loadStart, loadDone } = usePageLoading()
// 路由不重定向白名单
const whiteList = [
  '/home',
  '/Home',
  '/thirdplatform/oauth2/login',
  '/login',
  '/loginSale',
  '/social-login',
  '/auth-redirect',
  '/bind',
  '/register',
  '/su',
  '/exhibitionHome',
  '/ssoCallback',
  '/ssoError'
]
//token校验白名单
const tokenWhiteList = ['/ssoCallback', '/ssoError']

const heihestNextList = [
  //最高跳转权限名单，只要是以下的菜单直接跳转
  '/exhibitionHome'
]

let authorizedErrorCount = 0 //处理登录失败相关接口次数,2次以上才重新登录

// 路由加载前
router.beforeEach(async (to, from, next) => {
  const { t } = useI18n()
  start()
  loadStart()
  // 路由不重定向白名单
  if (heihestNextList.includes(to.path)) {
    next()
    return
  }
  //fix:解决哈希符号位置参数重复，如： /#?tenantId=1/
  if (!isEmpty(window.location.search)) {
    window.location.search = ''
  }

  // 处理国际化设置
  const urlQueryObj = GetQueryString() as OauthUrlQuery
  if (urlQueryObj?.lang) {
    const lang = urlQueryObj.lang as LocaleType
    if (localeStore.localeMap.some((item) => item.lang === lang)) {
      if (localeStore.localeMap.some((item) => item.lang === lang)) {
        localeStore.setCurrentLocale({ lang })
        const { changeLocale } = useLocale()
        changeLocale(lang)
      } else {
        console.warn(`国际化语言设置无效，语言 '${lang}' 不被支持`)
      }
    } else {
      console.warn(`国际化语言设置无效，语言 '${lang}' 不被支持`)
    }
  }

  if (getAccessToken() && !tokenWhiteList.includes(to.path)) {
    //如果是单点登录,直接跳转
    if (to.path === '/sso') {
      next()
      return
    }
    //登录后,跳转宣传首页时，直接跳转至首页
    if (to.path === '/home') {
      next({ path: '/' })
      return
    }

    // 如果现今已有登录的租户，而再有新的租户id链接登录，需要比对原登录的租户id是否和url中相同，如果不同，则需要登出重新登录
    if (whiteList.indexOf(to.path) === -1) {
      if (
        to.query.hasOwnProperty('tenantId') &&
        to.query.tenantId &&
        to.query.tenantId !== 'null' //目前不再需要url塞租户id进行登录，上多一层保险以免url中夹带租户id
      ) {
        if (authUtil.getTenantId() !== to.query.tenantId) {
          handleAuthorized()
        } else if (!authUtil.getTenantId()) {
          authUtil.setTenantId(to.query.tenantId as string)
          handleAuthorized()
        }
      }
    }

    const dictStore = useDictStoreWithOut()
    const userStore = useUserStoreWithOut()
    const permissionStore = usePermissionStoreWithOut()
    try {
      // 已经添加了动态路由，则正常跳转
      if (userStore.getIsSetUser) {
        // 处理组件路径对不上的情况
        if (to.meta.isEmptyComponent) {
          next('/404')
          return
        }
        next()
        return
      }

      // 获取所有字典
      if (!dictStore.getIsSetDict) {
        await dictStore.setDictMap()
      }
      if (!userStore.getIsSetUser) {
        isRelogin.show = true
        //获取用户信息(用户+角色信息)
        await userStore.setUserInfoAction()
        const userInfo = wsCache.get(CACHE_KEY.USER)

        const agentOperationStore = useAgentOperationStore()
        const matchObj = userInfo?.clients.find((el) => {
          //判断代客操作中,并且是国际版运维端,则更新store中的标识信息
          if (
            agentOperationStore.agentOperationMode &&
            el.oauthClient === 'umv-front-service-international' &&
            el.oauthClient.includes('international')
          ) {
            // 更新代客操作store中的国际版标识
            agentOperationStore.updateInternationalStatus(true, el.url)
          }

          return el.oauthClient === import.meta.env.VITE_APP_CLIENT_ID
        })
        //代客操作,处理国际版运维端

        if (!matchObj && !agentOperationStore.isInternational) {
          if (agentOperationStore.agentOperationMode) {
            ElMessageBox.alert(
              '代客操作模式失败，将重新切回超管账号,由于用户没有当前端权限',
              '提示',
              {
                confirmButtonText: '确定',
                callback: () => {
                  agentOperationStore.setLoginToken(true)
                  router.push({
                    path: '/'
                  })
                }
              }
            )
            return next() // 修改：确保调用 next() 并返回
          }
          next('/ssoError?noClient=1')
          return
        }
        // 后端过滤菜单
        await permissionStore.generateRoutes()
        permissionStore.getAddRouters.forEach((route) => {
          router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
        })
        const redirectPath = from.query.redirect || to.path
        const redirect = decodeURIComponent(redirectPath as string)
        const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
        next(nextData)
      }
    } catch (error) {
      console.log(error)

      //多次失败才会出发重新登录
      if (authorizedErrorCount++ > 1) {
        isRelogin.show = false
        handleAuthorized(t('sys.login.loginFail'))
        authorizedErrorCount = 0
      }
      next()
    }
  } else {
    if (whiteList.includes(to.path)) {
      console.log('白名单')

      next()
      return
    }

    //处理sso单点登录传递的租户id
    const tenantIds = authUtil.extractTenantIdsFromUrl(to.fullPath)
    if (!isEmpty(tenantIds)) {
      authUtil.removeTenantId()

      authUtil.setTenantId(tenantIds[0])
    }

    console.log('GetQueryString', GetQueryString())

    const urlObj = GetQueryString()

    //通过正则处理redirect后面出现 /?tenantId=xx和前面的tenantId冲突的问题
    //清除clientId，防止死循环
    const goLoginPath = (routerName) => {
      const basePath = `/${routerName ? routerName : 'home'}`
      const queryParams: string[] = []

      if (urlObj.hasOwnProperty('l')) {
        queryParams.push('l=1')
      }

      const redirectPath = to.fullPath
        .replace(/(\?|&)l=[^&?]*(&|$)/, '$1')
        .replace(/(\?|&)lang=[^&?]*(&|$)/, '$1')
        .replace(/(\?|&)tenantId=[^&?]*(&|$)/, '$1')
        .replace(/(\?|&)clientId=[^&?]*(&|$)/, '$1')

      queryParams.push(`redirect=${redirectPath}`)

      return `${basePath}${queryParams.length > 0 ? '?' + queryParams.join('&') : ''}`
    }

    let pathStr = ''
    if (urlObj.hasOwnProperty('loginType') && urlObj.loginType === 'mobile') {
      //手机登录
      pathStr = goLoginPath('login')
    } else if (urlObj.hasOwnProperty('loginType') && urlObj.loginType == 'saleMobile') {
      //销售端手机登录
      pathStr = goLoginPath('loginSale')
    } else {
      //登录首页-宣传页面
      pathStr = goLoginPath('home')
    }

    next(pathStr)
  }
})

router.afterEach((to) => {
  //更新当前路由业务场景
  const permissionStore = usePermissionStoreWithOut()
  permissionStore.updateCurrentRouteScene()

  useTitle(to?.meta?.title as string)
  done() // 结束Progress
  loadDone()
})
//解决部署后缓存问题
import { cacheManager } from '@/utils/cacheManager'

router.onError(async (error) => {
  if (import.meta.env.DEV) return

  const fetchResourcesErrors = [
    'Failed to fetch dynamically imported module',
    'Importing a module script failed'
  ]

  // 检查是否是资源加载错误
  const isResourceError = fetchResourcesErrors.some(
    (item) => error?.message && error.message?.includes(item)
  )

  if (isResourceError) {
    // 资源加载错误，直接刷新页面
    console.warn('检测到资源加载错误，直接刷新页面:', error.message)
    window.location.reload()
  } else {
    // 其他路由错误，进行版本检测
    console.warn('检测到路由错误，进行版本检测:', error.message)

    try {
      // 执行完整的版本检测和刷新逻辑
      await cacheManager.init()
    } catch (checkError) {
      console.error('版本检测失败，直接刷新页面:', checkError)
      window.location.reload()
    }
  }
})
