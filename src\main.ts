/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJack
 * @LastEditTime: 2024-01-03 14:14:54
 * @Description:
 */
//本地化 svg 图标
import 'virtual:svg-icons-register'

// 引入windi css
import '@/plugins/windi.css'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 element-plus
// import { setupElementPlus } from '@/plugins/elementPlus'

// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'

// 引入全局样式
import '@/styles/index.scss'

// 引入动画
import '@/plugins/animate.css'

// 路由
import router, { setupRouter } from '@/router'

// 权限
import { setupAuth } from '@/directives'

//全局变量
import { setGlobalProperties } from '@/config/globalProperties'

import { createApp } from 'vue'

import App from './App.vue'
// 导入缓存管理器
import { cacheManager } from '@/utils/cacheManager'
// 初始化缓存管理
cacheManager.init()

import './permission'

import Logger from '@/utils/Logger'

const setupAll = async () => {
  //全局变量
  setGlobalProperties(app)

  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  // setupElementPlus(app)

  setupFormCreate(app)

  setupRouter(app)

  setupAuth(app)

  await router.isReady()

  app.mount('#app')
  return app
}

// 创建实例
const app = createApp(App)
setupAll()

const { t } = useI18n()
Logger.prettyPrimary(t('sys.welcomeUse'), import.meta.env.VITE_APP_TITLE)

export default app
