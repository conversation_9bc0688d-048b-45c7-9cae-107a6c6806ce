<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-25 11:47:51
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-06-27 15:28:06
 * @Description: 
-->
<script setup lang="ts">
defineOptions({
  name: 'APP'
})

// import { isDark } from '@/utils/is'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('app')
const appStore = useAppStore()
const currentSize = computed(() => appStore.getCurrentSize)
const greyMode = computed(() => appStore.getGreyMode)
const { wsCache } = useCache()

// 根据浏览器当前主题设置系统主题色
const setDefaultTheme = () => {
  let isDarkTheme = wsCache.get(CACHE_KEY.IS_DARK)
  if (isDarkTheme === null) {
    //检测浏览器设置
    // isDarkTheme = isDark()
    //todo:强制默认非Dark模式
    isDarkTheme = false
  }
  appStore.setIsDark(isDarkTheme)
}
setDefaultTheme()

//水印
import envController from '@/controller/envController'
import { useWatermark } from '@/hooks/web/useWatermark'
import { useI18n } from '@/hooks/web/useI18n'
const { setWatermark } = useWatermark()
const { t } = useI18n()

onMounted(() => {
  const envName = envController.getEnvironment()

  if (envName === 'pro') {
    setWatermark(t('watermark.unauthorizedAccess'))
  } else {
    setWatermark(envName.toUpperCase() + '环境')
  }
})
</script>
<template>
  <ConfigGlobal :size="currentSize">
    <RouterView :class="greyMode ? `${prefixCls}-grey-mode` : ''" />
  </ConfigGlobal>
</template>
<style lang="scss">
$prefix-cls: #{$namespace}-app;
.size {
  width: 100%;
  height: 100%;
}

html,
body {
  padding: 0 !important;
  margin: 0;
  overflow: hidden;
  @extend .size;

  #app {
    @extend .size;
  }
}

.#{$prefix-cls}-grey-mode {
  filter: grayscale(100%);
}
</style>
